import React, { useState } from "react";
import { HiChevronLeft, HiChevronRight } from "react-icons/hi2";

interface ImageGalleryProps {
  className?: string;
}

interface ChartData {
  id: string;
  title: string;
  value: string;
  chartContent: React.ReactNode;
}

function ImageGallery({ className = "" }: ImageGalleryProps) {
  // Chart data for the 5 different chart types
  const [chartData] = useState<ChartData[]>([
    {
      id: "break-even",
      title: "Break Even Percentage",
      value: "00",
      chartContent: <div className="text-6xl">📊</div>,
    },
    {
      id: "average-profit",
      title: "Average Profit",
      value: "00",
      chartContent: <div className="text-6xl">📈</div>,
    },
    {
      id: "most-common",
      title: "Most Common Profit",
      value: "00",
      chartContent: <div className="text-6xl">💰</div>,
    },
    {
      id: "string-slope",
      title: "String Slope",
      value: "00",
      chartContent: <div className="text-6xl">📉</div>,
    },
    {
      id: "profit-per-pick",
      title: "Profit Per Correct Pick",
      value: "00",
      chartContent: <div className="text-6xl">🎯</div>,
    },
  ]);

  const [currentChartIndex, setCurrentChartIndex] = useState(0);

  const handlePrevChart = () => {
    setCurrentChartIndex((prev) =>
      prev > 0 ? prev - 1 : chartData.length - 1
    );
  };

  const handleNextChart = () => {
    setCurrentChartIndex((prev) =>
      prev < chartData.length - 1 ? prev + 1 : 0
    );
  };

  const currentChart = chartData[currentChartIndex];

  return (
    <div className={`bg-[#051844] py-8 sm:py-12 ${className}`}>
      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6">
        {/* Generated Parlays Header */}
        <div className="bg-gradient-to-r from-[#233e6c] to-[#1a2d54] rounded-t-xl p-6 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <span className="text-white text-2xl">▶</span>
            <h3 className="text-white text-2xl font-bold">Generated Parlays</h3>
          </div>
        </div>

        {/* Chart Statistics Row */}
        <div className="bg-gradient-to-r from-[#1a2d54] to-[#233e6c] px-6 py-4">
          <div className="grid grid-cols-5 gap-4 text-center">
            {chartData.map((chart) => (
              <div key={chart.id} className="text-white">
                <div className="text-3xl font-bold mb-1">{chart.value}</div>
                <div className="text-sm font-medium">{chart.title}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Main Chart Card Container */}
        <div className="bg-gradient-to-b from-[#233e6c] to-[#1a2d54] rounded-b-xl p-8 relative min-h-[500px]">
          {/* Navigation Arrows */}
          <button
            onClick={handlePrevChart}
            className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 p-3 bg-white/10 hover:bg-white/20 text-white rounded-full transition-all duration-300 hover:scale-110"
          >
            <HiChevronLeft className="w-8 h-8" />
          </button>

          <button
            onClick={handleNextChart}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 p-3 bg-white/10 hover:bg-white/20 text-white rounded-full transition-all duration-300 hover:scale-110"
          >
            <HiChevronRight className="w-8 h-8" />
          </button>
          {/* Chart Card Stack */}
          <div className="flex items-center justify-center h-full">
            <div className="relative w-full max-w-4xl">
              {/* Current Chart Card */}
              <div className="bg-gradient-to-b from-[#051844] to-[#0a1a52] rounded-xl p-8 text-center shadow-2xl">
                <h2 className="text-white text-4xl font-bold mb-8">
                  {currentChart.title}
                </h2>

                {/* Chart Content Area */}
                <div className="bg-[#051844]/50 rounded-lg p-12 mb-8 min-h-[300px] flex items-center justify-center">
                  {currentChart.chartContent}
                </div>

                {/* Chart Navigation Dots */}
                <div className="flex justify-center gap-2 mb-4">
                  {chartData.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentChartIndex(index)}
                      className={`w-3 h-3 rounded-full transition-all duration-200 ${
                        index === currentChartIndex
                          ? "bg-white"
                          : "bg-white/30 hover:bg-white/50"
                      }`}
                    />
                  ))}
                </div>

                {/* Chart Counter */}
                <div className="text-white/70 text-sm">
                  {currentChartIndex + 1} of {chartData.length}
                </div>
              </div>

              {/* Background Cards (Stacked Effect) */}
              <div className="absolute inset-0 -z-10">
                <div className="bg-gradient-to-b from-[#051844] to-[#0a1a52] rounded-xl transform translate-x-2 translate-y-2 opacity-30 w-full h-full"></div>
                <div className="bg-gradient-to-b from-[#051844] to-[#0a1a52] rounded-xl transform translate-x-4 translate-y-4 opacity-15 w-full h-full absolute inset-0"></div>
              </div>
            </div>
          </div>

          {/* Create Comparison Button */}
          <div className="mt-8 flex justify-center">
            <button className="bg-[#233e6c] hover:bg-[#1a2d54] text-white font-bold py-3 px-8 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl">
              Create Comparison
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ImageGallery;
