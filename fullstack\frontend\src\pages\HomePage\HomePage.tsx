import React, { useState, useEffect } from "react";
import { HiBars3 } from "react-icons/hi2";
import { useAuth } from "../../contexts/AuthContext";
import { usePicks } from "../../contexts/PicksContext";
import { useSidebar } from "../../contexts/SidebarContext";
import HeroSection from "./HeroSection";
import GeneratedParlaysSection from "./GeneratedParlaysSection";
import ImageGallery from "../../components/ImageGallery";
import WhereToBet from "./WhereToBet";

interface SubparlayPick {
  pID: string | number;
  odds: string;
  confidence?: number;
  name?: string;
  bayesian_conf?: number;
  bayesian_prob?: number;
  logistic_prob?: number;
  capital_limit?: number;
  event_id?: string;
  gameID?: number;
  league?: string;
  reusable?: boolean;
  stat_type?: string;
}

interface SubparlayColumn extends Array<SubparlayPick> {}

interface OptimizerData {
  best_score: number;
  split_index?: number;
  subparlays: SubparlayColumn[];
}

function HomePage() {
  const { navigateToView } = useAuth();
  const { getPicks, removePick, totalPicksCount } = usePicks();
  const userPicks = getPicks();
  const { toggleSidebar, isOpen: sidebarOpen } = useSidebar();

  // Optimizer state
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [subparlays, setSubparlays] = useState<SubparlayColumn[]>([]);
  const [optimizerError, setOptimizerError] = useState<string>("");
  const [isParlaysExpanded, setIsParlaysExpanded] = useState(false);
  const [showChartsGallery, setShowChartsGallery] = useState(false);

  const API_BASE_URL = "/api";

  const handleAddPicksClick = () => {
    navigateToView("addPicks");
  };

  const handleOptimizeParlays = async () => {
    if (totalPicksCount < 2) {
      setOptimizerError("Need at least 2 picks to optimize parlays");
      return;
    }

    setIsOptimizing(true);
    setOptimizerError("");
    setSubparlays([]);

    try {
      // Load user picks from PicksContext into backend for optimization
      const loadResponse = await fetch(`${API_BASE_URL}/load_user_picks`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ picks: userPicks }),
      });

      if (!loadResponse.ok) {
        throw new Error(`Failed to load user picks: ${loadResponse.status}`);
      }

      const loadData = await loadResponse.json();
      console.log("Loaded user picks for optimization:", loadData.message);

      // Now run the optimizer
      const optimizeResponse = await fetch(`${API_BASE_URL}/optimize_split`);
      if (!optimizeResponse.ok) {
        throw new Error(`Failed to optimize: ${optimizeResponse.status}`);
      }

      const data: OptimizerData = await optimizeResponse.json();

      // Process subparlays with the same data structure
      const mappedSubparlays = (data.subparlays || []).map((subparlay: any[]) =>
        subparlay.map((pick: any) => ({
          ...pick,
          pID: pick.pID,
          name: pick.name, // Already formatted in backend
          confidence: pick.confidence,
          odds: pick.odds?.toString() || "1.85",
        }))
      );

      console.log("Subparlays with user data:", mappedSubparlays);
      setSubparlays(mappedSubparlays);

      // Auto-expand parlays section when optimization completes successfully
      setIsParlaysExpanded(true);

      // Show charts gallery after successful optimization
      setShowChartsGallery(true);
    } catch (error) {
      console.error("Error optimizing parlays:", error);
      setOptimizerError(
        `Error optimizing parlays: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    } finally {
      setIsOptimizing(false);
    }
  };

  return (
    <div className="relative min-h-screen bg-[#051844]">
      {/* Fixed Hamburger Menu */}
      <div className="fixed top-4 left-4 sm:top-6 sm:left-6 z-50">
        {!sidebarOpen && (
          <HiBars3
            className="w-auto h-12 sm:h-14 text-white bg-[#233e6c] rounded-lg hover:text-white transition-colors duration-300 cursor-pointer p-2"
            onClick={() => toggleSidebar()}
          />
        )}
      </div>

      {/* Section 1: Hero Section */}
      <div className="relative mt-[-5%]">
        <HeroSection
          totalPicksCount={totalPicksCount}
          userPicks={userPicks}
          isOptimizing={isOptimizing}
          optimizerError={optimizerError}
          onAddPicksClick={handleAddPicksClick}
          onOptimizeParlays={handleOptimizeParlays}
          onRemovePick={removePick}
        />
      </div>

      {/* Section 2: Generated Parlays Section */}
      <div className="relative">
        <GeneratedParlaysSection
          subparlays={subparlays}
          isParlaysExpanded={isParlaysExpanded}
          onToggleExpanded={() => setIsParlaysExpanded(!isParlaysExpanded)}
        />
      </div>

      {/* Section 3: Image Gallery Section */}
      {showChartsGallery && (
        <div className="relative">
          <ImageGallery />
        </div>
      )}

      {/* Section 4: Where To Bet Section */}
      <div className="relative -mt-2">
        <WhereToBet subparlays={subparlays} />
      </div>
    </div>
  );
}

export default HomePage;
